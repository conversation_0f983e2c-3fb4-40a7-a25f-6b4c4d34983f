/* Collection Page - Sharp Rectangular Design */
.collectionPage {
  min-height: 100vh;
  background: #ffffff;
  padding-top: 100px; /* Account for fixed header */
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #4a3728;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a3728;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 4rem 2rem;
  color: #4a3728;
}

.errorContainer h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Collection Header */
.collectionHeader {
  margin-bottom: 3rem;
  padding: 2rem;
  background: #f8f8f8;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

.headerContent {
  text-align: center;
}

.collectionTitle {
  font-size: 3rem;
  font-weight: 700;
  color: #4a3728;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.collectionDescription {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1.5rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.collectionMeta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.productCount {
  font-weight: 600;
  color: #4a3728;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #4a3728;
  border-radius: 0; /* Sharp corners */
}

.collectionLevel {
  font-weight: 600;
  color: #666;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
}

/* Filter Section */
.filterSection {
  margin-bottom: 2rem;
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
}

.searchBar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: #fafafa;
  border-bottom: 1px solid #ddd;
}

.searchInput {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  color: #666;
  z-index: 1;
}

.searchField {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 1rem;
  background: white;
  transition: border-color 0.3s ease;
}

.searchField:focus {
  outline: none;
  border-color: #4a3728;
}

.filterToggle {
  background: #4a3728;
  color: white;
  border: 2px solid #4a3728;
  padding: 1rem 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.filterToggle:hover {
  background: white;
  color: #4a3728;
}

.filterToggle.active {
  background: #6b4e3d;
}

/* Advanced Filters */
.advancedFilters {
  padding: 2rem;
  background: white;
  border-top: 1px solid #ddd;
}

.filterGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  align-items: end;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterLabel {
  font-weight: 600;
  color: #4a3728;
  font-size: 0.9rem;
}

.priceRange {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.priceInput {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  font-size: 0.9rem;
}

.priceInput:focus {
  outline: none;
  border-color: #4a3728;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #4a3728;
  cursor: pointer;
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: #4a3728;
}

.sortControls {
  display: flex;
  gap: 0.5rem;
}

.sortSelect {
  flex: 1;
  padding: 0.75rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: white;
  font-size: 0.9rem;
}

.sortSelect:focus {
  outline: none;
  border-color: #4a3728;
}

.sortDirection {
  background: #4a3728;
  color: white;
  border: 2px solid #4a3728;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
}

.sortDirection:hover {
  background: white;
  color: #4a3728;
}

.sortDirection.desc {
  transform: rotate(180deg);
}

.clearFilters {
  background: #dc3545;
  color: white;
  border: 2px solid #dc3545;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  width: 100%;
}

.clearFilters:hover {
  background: white;
  color: #dc3545;
}

/* Products Section */
.productsSection {
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .collectionTitle {
    font-size: 2.5rem;
  }
  
  .filterGrid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .collectionHeader {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .collectionTitle {
    font-size: 2rem;
  }
  
  .collectionDescription {
    font-size: 1rem;
  }
  
  .collectionMeta {
    flex-direction: column;
    gap: 1rem;
  }
  
  .searchBar {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .searchInput {
    width: 100%;
  }
  
  .filterToggle {
    width: 100%;
    justify-content: center;
  }
  
  .filterGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .priceRange {
    flex-direction: column;
    align-items: stretch;
  }
  
  .sortControls {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .collectionTitle {
    font-size: 1.75rem;
  }
  
  .advancedFilters {
    padding: 1rem;
  }
  
  .searchField {
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  }
  
  .filterToggle {
    padding: 0.75rem 1rem;
  }
}
