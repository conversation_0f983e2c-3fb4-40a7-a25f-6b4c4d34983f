/* Patina Selector - Sharp Rectangular Design */
.patinaSelector {
  margin: 2rem 0;
  padding: 2rem;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  background: #fafafa;
}

.selectorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #4a3728;
}

.selectorTitle {
  font-size: 1.3rem;
  font-weight: 700;
  color: #4a3728;
  margin: 0;
}

.selectedPatina {
  font-size: 1rem;
  font-weight: 600;
  color: #4a3728;
  background: white;
  padding: 0.5rem 1rem;
  border: 2px solid #4a3728;
  border-radius: 0; /* Sharp corners */
}

/* Patina Grid */
.patinaGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.patinaOption {
  background: white;
  border: 2px solid #ddd;
  border-radius: 0; /* Sharp corners */
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  text-align: center;
  min-height: 100px;
}

.patinaOption:hover {
  border-color: #4a3728;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 55, 40, 0.15);
}

.patinaOption.selected {
  border-color: #4a3728;
  border-width: 3px;
  background: #f8f6f3;
  box-shadow: 0 4px 12px rgba(74, 55, 40, 0.2);
}

.patinaColor {
  width: 40px;
  height: 40px;
  border: 2px solid #333;
  border-radius: 0; /* Sharp corners */
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.patinaName {
  font-size: 0.85rem;
  font-weight: 600;
  color: #4a3728;
  line-height: 1.2;
}

.patinaOption.selected .patinaName {
  color: #4a3728;
  font-weight: 700;
}

/* Patina Note */
.patinaNote {
  background: #fff3cd;
  border: 2px solid #ffeaa7;
  border-radius: 0; /* Sharp corners */
  padding: 1rem;
  margin-top: 1rem;
}

.patinaNote p {
  margin: 0;
  font-size: 0.9rem;
  color: #856404;
  line-height: 1.4;
}

.patinaNote strong {
  color: #533f03;
}

/* Responsive Design */
@media (max-width: 768px) {
  .patinaSelector {
    padding: 1.5rem;
  }
  
  .selectorHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    text-align: center;
  }
  
  .patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.75rem;
  }
  
  .patinaOption {
    padding: 0.75rem;
    min-height: 80px;
    gap: 0.5rem;
  }
  
  .patinaColor {
    width: 30px;
    height: 30px;
  }
  
  .patinaName {
    font-size: 0.8rem;
  }
  
  .selectorTitle {
    font-size: 1.1rem;
  }
  
  .selectedPatina {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .patinaGrid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 0.5rem;
  }
  
  .patinaOption {
    padding: 0.5rem;
    min-height: 70px;
    gap: 0.4rem;
  }
  
  .patinaColor {
    width: 25px;
    height: 25px;
  }
  
  .patinaName {
    font-size: 0.75rem;
  }
  
  .patinaNote {
    padding: 0.75rem;
  }
  
  .patinaNote p {
    font-size: 0.8rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .patinaOption {
    transition: none;
  }
  
  .patinaOption:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .patinaOption {
    border-width: 3px;
  }
  
  .patinaOption.selected {
    border-width: 4px;
  }
  
  .patinaColor {
    border-width: 3px;
  }
}
