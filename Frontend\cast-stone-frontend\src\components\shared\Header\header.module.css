/* Magazine/Editorial Theme Variables */
.Headerroot {
  --cast-stone-brown: #4a3728;
  --cast-stone-light-brown: #6b4e3d;
  --cast-stone-cream: #faf9f7;
  --cast-stone-white: #ffffff;
  --cast-stone-gray: #8b7355;
  --cast-stone-shadow: rgba(74, 55, 40, 0.1);
  --cast-stone-shadow-hover: rgba(74, 55, 40, 0.15);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header Styles - Completely transparent design */
.header {
  background: transparent;
  backdrop-filter: none;
  border: none;
  padding: 0;
  position: relative;
  z-index: 1000;
  transition: all 0.3s ease;
}

/* Header when scrolled - stays transparent */
.header.scrolled {
  background: transparent;
  backdrop-filter: none;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo Styles */
.logo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.logoLink {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.95);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.scrolled .logoLink {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.logoLink:hover {
  transform: translateY(-1px);
}

.logoText {
  font-size: 1.8rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  line-height: 1;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  text-transform: uppercase;
}

.logoSubtext {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: var(--cast-stone-gray);
  margin-top: 2px;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
}

/* Navigation Styles */
.nav {
  display: flex;
  align-items: center;
}

.navList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
  align-items: center;
}

.navItem {
  position: relative;
  display: flex;
  align-items: center;
}

.navLink,
.navButton {
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.scrolled .navLink,
.scrolled .navButton {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.navLink:hover,
.navButton:hover {
  color: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.scrolled .navLink:hover,
.scrolled .navButton:hover {
  color: rgba(255, 255, 255, 1);
  text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.8);
}

.navLink::after,
.navButton::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.scrolled .navLink::after,
.scrolled .navButton::after {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
}

.navLink:hover::after,
.navButton:hover::after,
.navButton.active::after {
  width: 80%;
}

/* Dropdown Styles */
.dropdownContainer {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdownIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.dropdownIcon.rotated {
  transform: rotate(180deg);
  color: rgba(255, 255, 255, 0.9);
}

.loadingIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--cast-stone-white);
  border: 1px solid rgba(74, 55, 40, 0.1);
  border-radius: 8px;
  box-shadow: 0 8px 32px var(--cast-stone-shadow-hover);
  min-width: 280px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-10px);
  transition: var(--transition-smooth);
  animation: dropdownSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  backdrop-filter: blur(10px);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
  }
}

.dropdownList {
  list-style: none;
  margin: 0;
  padding: 0.5rem 0;
}

.dropdownItem {
  position: relative;
}

.dropdownLink {
  display: block;
  padding: 0.75rem 1.25rem;
  color: var(--cast-stone-brown);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.dropdownLink:hover {
  background: rgba(74, 55, 40, 0.04);
  color: var(--cast-stone-light-brown);
  border-left-color: var(--cast-stone-brown);
  transform: translateX(2px);
}

/* Sub-dropdown Styles */
.subDropdownList {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(74, 55, 40, 0.02);
  border-top: 1px solid rgba(74, 55, 40, 0.08);
}

.subDropdownItem {
  position: relative;
}

.subDropdownLink {
  display: block;
  padding: 0.6rem 1.25rem 0.6rem 2rem;
  color: var(--cast-stone-gray);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
  position: relative;
}

.subDropdownLink::before {
  content: '→  ';
  position: absolute;
  left: 0.75rem;
  color: var(--cast-stone-gray);
  font-size: 0.7rem;
  transition: var(--transition-fast);
}

.subDropdownLink:hover {
  background: rgba(74, 55, 40, 0.06);
  color: var(--cast-stone-brown);
  border-left-color: var(--cast-stone-light-brown);
  transform: translateX(2px);
}

.subDropdownLink:hover::before {
  color: var(--cast-stone-brown);
  transform: translateX(2px);
}

/* Sub-sub-dropdown Styles (Level 3) */
.subSubDropdownList {
  list-style: none;
  margin: 0;
  padding: 0;
  background: rgba(74, 55, 40, 0.04);
  border-top: 1px solid rgba(74, 55, 40, 0.12);
}

.subSubDropdownItem {
  position: relative;
}

.subSubDropdownLink {
  display: block;
  padding: 0.5rem 1.25rem 0.5rem 2.5rem;
  color: var(--cast-stone-gray);
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 400;
  transition: var(--transition-fast);
  border-left: 6px solid transparent;
  position: relative;
}

.subSubDropdownLink::before {
  content: '⤷  ';
  position: absolute;
  left: 1.5rem;
  color: var(--cast-stone-gray);
  font-size: 0.65rem;
  transition: var(--transition-fast);
}

.subSubDropdownLink:hover {
  background: rgba(74, 55, 40, 0.08);
  color: var(--cast-stone-brown);
  border-left-color: var(--cast-stone-light-brown);
  transform: translateX(2px);
}

.subSubDropdownLink:hover::before {
  color: var(--cast-stone-brown);
  transform: translateX(2px);
}

/* Cart Styles */
.cartContainer {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.cartLink {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  color: var(--cast-stone-brown);
  text-decoration: none;
  border-radius: 8px;
  transition: var(--transition-smooth);
  position: relative;
}

.cartLink:hover {
  background: rgba(74, 55, 40, 0.06);
  color: var(--cast-stone-light-brown);
  transform: translateY(-1px);
}

.cartIconWrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cartBadge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #dc2626;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  animation: cartBadgeAppear 0.3s ease-out;
}

@keyframes cartBadgeAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .navList {
    gap: 0;
  }

  .navLink,
  .navButton {
    padding: 1.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
    height: 70px;
  }

  .logoText {
    font-size: 1.75rem;
  }

  .logoSubtext {
    font-size: 0.7rem;
  }

  .navList {
    gap: 0;
  }

  .navLink,
  .navButton {
    padding: 1.25rem 0.75rem;
    font-size: 0.85rem;
  }

  .dropdown {
    min-width: 200px;
  }
}

@media (max-width: 640px) {
  .nav {
    display: none; /* Will implement mobile menu in future */
  }

  .container {
    justify-content: space-between;
  }
}
