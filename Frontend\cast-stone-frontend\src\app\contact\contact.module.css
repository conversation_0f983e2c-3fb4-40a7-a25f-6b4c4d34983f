/* Contact Page Styles - GuardianJet Inspired Design */
.contactPage {
  --navy-blue:hsl(224, 64.30%, 32.90%);
  --dark-navy: #1e40af;
  --light-navy: #3b82f6;
  --white: #ffffff;
  --light-gray: #f8fafc;
  --medium-gray: #64748b;
  --dark-gray: #334155;
  --border-gray: #e2e8f0;
  --shadow: rgba(30, 58, 138, 0.1);
  --shadow-hover: rgba(30, 58, 138, 0.15);
  --transition: all 0.3s ease;

  min-height: 100vh;
  background-color: var(--white);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* Main Content */
.mainContent {
  padding: 2rem 0;
  background-color: var(--white);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contentGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
  min-height: 80vh;
}

/* Left Side - Info Section */
.infoSection {
  padding: 2rem 0;
}

.heroText {
  margin-bottom: 2rem;
}

.heroTitle {
  font-size: 1.5rem;
  font-weight: 400;
  color: var(--dark-gray);
  line-height: 1.6;
  margin-bottom: 2rem;
}

/* Contact Cards */
.contactCards {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contactCard {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--light-gray);
  border: 1px solid var(--border-gray);
  border-radius: 8px;
}

.cardIcon {
  width: 24px;
  height: 24px;
  color: var(--navy-blue);
  flex-shrink: 0;
}

.cardContent {
  flex: 1;
}

.cardTitle {
  font-weight: 600;
  color: var(--dark-gray);
  margin: 0;
  font-size: 0.95rem;
}

.cardSubtitle {
  color: var(--medium-gray);
  margin: 0;
  font-size: 0.9rem;
}

/* Company Description */
.companyDescription {
  margin-top: 2rem;
}

.descriptionText {
  color: var(--dark-gray);
  line-height: 1.6;
  margin-bottom: 1rem;
  font-size: 0.95rem;
}

.descriptionText:last-child {
  margin-bottom: 0;
}

/* Right Side - Form Section */
.formSection {
  padding: 2rem 0;
}

.formCard {
  background: var(--light-gray);
  border-radius: 8px;
  padding: 2rem;
  border: 1px solid var(--border-gray);
}

.formTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.requiredField {
  color: var(--medium-gray);
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

/* Form Styles */
.contactForm {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--dark-gray);
  margin-bottom: 0.5rem;
}

.input,
.select,
.textarea {
  font-size: 0.95rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-gray);
  border-radius: 4px;
  background-color: var(--white);
  color: var(--dark-gray);
  transition: var(--transition);
  outline: none;
  width: 100%;
}

.input:focus,
.select:focus,
.textarea:focus {
  border-color: var(--navy-blue);
  box-shadow: 0 0 0 2px rgba(30, 58, 138, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.textarea::placeholder {
  color: var(--medium-gray);
  opacity: 0.8;
}

.select {
  cursor: pointer;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23334155" stroke-width="2"><polyline points="6,9 12,15 18,9"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px;
  appearance: none;
}

/* Disclaimer */
.disclaimer {
  margin: 1rem 0;
}

.disclaimerText {
  font-size: 0.8rem;
  color: var(--medium-gray);
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.disclaimerText:last-child {
  margin-bottom: 0;
}

/* Submit Button */
.submitButton {
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.875rem 2rem;
  background: var(--navy-blue);
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.5rem;
  align-self: flex-start;
}

.submitButton:hover:not(:disabled) {
  background: #c19660;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.submitting {
  background: var(--medium-gray) !important;
}

/* Submit Message */
.submitMessage {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid;
}

.submitMessage.success {
  background-color: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.submitMessage.error {
  background-color: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .contentGrid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .infoSection {
    order: 2;
  }

  .formSection {
    order: 1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .mainContent {
    padding: 1rem 0;
  }

  .formCard {
    padding: 1.5rem;
  }

  .heroTitle {
    font-size: 1.25rem;
  }

  .contactCards {
    gap: 0.75rem;
  }

  .contactCard {
    padding: 0.75rem;
  }

  .descriptionText {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .formCard {
    padding: 1rem;
  }

  .heroTitle {
    font-size: 1.1rem;
  }

  .formTitle {
    font-size: 1.25rem;
  }

  .input,
  .select,
  .textarea {
    padding: 0.625rem 0.75rem;
    font-size: 0.9rem;
  }

  .submitButton {
    padding: 0.75rem 1.5rem;
    font-size: 0.85rem;
  }
}
