/* Product Page Styles - Sharp Rectangular Design */
.productPage {
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #4a3728;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a3728;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 4rem 2rem;
  color: #4a3728;
}

.errorContainer h1 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Main Product Layout */
.productMain {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 4rem;
}

.imageSection {
  position: relative;
}

.detailsSection {
  padding: 2rem 0;
}

/* Product Title */
.productTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

/* Product Code */
.productCode {
  background: #f5f5f5;
  border: 1px solid #ddd;
  padding: 0.75rem 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #4a3728;
  margin-bottom: 2rem;
  border-radius: 0; /* Sharp corners */
}

/* Product Info Grid */
.productInfo {
  margin-bottom: 2rem;
}

.infoRow {
  display: grid;
  grid-template-columns: 150px 1fr;
  padding: 0.75rem 0;
  border-bottom: 1px solid #eee;
}

.infoLabel {
  font-weight: 600;
  color: #4a3728;
  font-size: 0.9rem;
}

.infoValue {
  color: #333;
  font-size: 0.9rem;
}

/* Price Section */
.priceSection {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f8f8;
  border: 2px solid #4a3728;
  border-radius: 0; /* Sharp corners */
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: #4a3728;
}

/* Purchase Section */
.purchaseSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-top: 2rem;
}

.quantitySelector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantitySelector label {
  font-weight: 600;
  color: #4a3728;
}

.quantityControls {
  display: flex;
  align-items: center;
  border: 2px solid #4a3728;
  border-radius: 0; /* Sharp corners */
  overflow: hidden;
}

.quantityBtn {
  background: #4a3728;
  color: white;
  border: none;
  padding: 0.75rem 1rem;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: bold;
  transition: background-color 0.2s;
}

.quantityBtn:hover:not(:disabled) {
  background: #6b4e3d;
}

.quantityBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.quantityInput {
  border: none;
  padding: 0.75rem 1rem;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
  width: 80px;
  background: white;
  outline: none;
}

.addToCartBtn {
  background: #4a3728;
  color: white;
  border: 2px solid #4a3728;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0; /* Sharp corners */
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.addToCartBtn:hover:not(:disabled) {
  background: white;
  color: #4a3728;
}

.addToCartBtn:disabled {
  background: #ccc;
  border-color: #ccc;
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .productMain {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .container {
    padding: 1rem;
  }
  
  .productTitle {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .infoRow {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }
  
  .infoLabel {
    font-weight: 700;
  }
  
  .purchaseSection {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 1rem;
    border-top: 2px solid #4a3728;
    margin: 2rem -1rem 0;
  }
  
  .quantitySelector {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .quantityControls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .productTitle {
    font-size: 1.5rem;
  }
  
  .price {
    font-size: 1.5rem;
  }
  
  .productCode {
    font-size: 0.8rem;
    padding: 0.5rem;
  }
}
